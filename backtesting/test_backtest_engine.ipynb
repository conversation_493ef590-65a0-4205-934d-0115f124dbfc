%reload_ext autoreload
%autoreload 2

import datetime
import mattlibrary.backtesting.backtest_engine as backtest_engine
from mattlibrary.backtesting.backtest_configuration import BacktestConfiguration

#backtest parameters
configuration = BacktestConfiguration(
    starting_balance=100000,
    base_currency="USD",
    symbols_data=['AUDUSD', 'AUDNZD', 'AUDCAD', 'AUDC<PERSON>', 'AUDJPY', 'EURAUD', 'GBPAUD',
                'NZDUSD', 'NZDCAD', 'NZDCHF', 'NZDJPY', 'EURNZD', 'GBPNZD', 
                'CADCHF', 'CADJPY', 'EURCAD', 'GBPCAD', 'USDCAD',
                'CHFJPY', 'EURCHF', 'GBPCHF', 'USDCHF',
                'EURJPY', 'EURUSD', 'EURGBP', 
                'GBPJPY', 'GBPUSD',
                'USDJPY'],

    symbols_traded=['AUDUSD', 'AUDNZD', 'AUDCAD', 'AUDC<PERSON>', 'AUDJPY', 'EURAUD', 'GBPAUD',
                'NZDUSD', 'NZDCAD', 'NZDC<PERSON>', 'NZDJPY', 'EURNZD', 'GBPNZD', 
                'CADC<PERSON>', 'CADJPY', 'EURCAD', 'GBPCAD', 'USDCAD',
                'CHFJPY', 'EURCHF', 'GBPCHF', 'USDCHF',
                'EURJPY', 'EURUSD', 'EURGBP', 
                'GBPJPY', 'GBPUSD',
                'USDJPY'],
    
    execution_plugin_type="SimulatedExecutionEngine",    

    #environment parameters
    backtests_base_directory="/home/<USER>/development/python_development/backtests/",
    clean_directory_on_init=True,

    # Data parameters
    data_directory = "/mnt/sambashare/nas/storage/Share2_FinancialData1/Dukascopy/Currencies/dukascopy_fx_polars_db/",
    dataset_name="dukascopy_daily_fx",
    start_dt=datetime.datetime(2000, 5, 3),
    end_dt=datetime.datetime(2023, 12, 31),
    
    # Strategy parameters
    sma_window_size=30,
    order_size=100_000,
    is_target_size=False,
    
    # Logging
    logging_enabled=False,
    log_to_console=False,
    log_to_file=True,
    strategy_excel_logging=False,
    statistics_excel_logging=False,
    
    # Performance tracking
    track_performance=True,
    visualize_performance=False
)

#new backtest
#initialize backtest engine
engine = backtest_engine.BacktestEngine(configuration)

#start backtest
engine.start_backtest()

child_orders = engine.child_orders

child_orders.head()