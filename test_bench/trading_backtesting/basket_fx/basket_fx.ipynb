{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/lib/python3/dist-packages/scipy/__init__.py:146: UserWarning:\n", "\n", "A NumPy version >=1.17.3 and <1.25.0 is required for this version of SciPy (detected version 1.26.1\n", "\n"]}], "source": ["import datetime\n", "import polars as pl\n", "from mattlibrary.backtesting.enumerables import DataType\n", "from mattlibrary.backtesting.base_strategy import StrategyBase\n", "from mattlibrary.backtesting.backtest_engine import BacktestEngine\n", "from mattlibrary.datamanagement.clickhouse import ClickHouseClient\n", "\n", "%reload_ext autoreload\n", "%autoreload 2\n", "\n", "clickhouse_client = ClickHouseClient()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class MyStrategy(StrategyBase):\n", "\n", "    def __init__(self, symbolIds:list[str]):\n", "        self.symbolIds = symbolIds\n", "        self.strategy_id = \"BasketFxStrategy\"\n", "        self.description = \"Trades FX Baskets\"\n", "        super().__init__(self.strategy_id, self.description)\n", "          \n", "               \n", "    def initialize(self):\n", "        self.position_size = 100_000\n", "        \n", "        \n", "    def process_data(self, data):\n", "        \n", "        symbol = data[\"symbol\"]\n", "        timestamp = data[\"datetime\"]\n", "        price = data[\"close\"]\n", "\n", "                    \n", "    def finalize(self):\n", "        pass"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["base_currency = \"CAD\"\n", "starting_balance = 100_000\n", "symbol_ids = ['AUDUSD', 'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY', 'EURAUD', 'GBPAUD',\n", "              'NZDUSD', 'NZDCAD', 'NZDCHF', 'NZDJPY', 'EURNZD', 'GBPNZD', \n", "              'CADCHF', 'CADJPY', 'EURCAD', 'GBPCAD', 'USDCAD',\n", "              'CHFJPY', 'EURCHF', 'GBPCHF', 'USDCHF',\n", "              'EURJPY', 'EURUSD', 'EURGBP', \n", "              'GBPJPY', 'GBPUSD',\n", "              'USDJPY']\n", "\n", "table_name = \"dukascopy_fx_ohlc_daily\"\n", "datetime_from = datetime.datetime(2010, 1, 1)\n", "datetime_to = datetime.datetime(2022, 12, 31)\n", "sma_window_size = 15\n", "track_performance = True\n", "calculate_performance = True\n", "visualize_performance = True\n", "\n", "#data\n", "data = clickhouse_client.request_marketdata(table_name, symbol_ids, datetime_from, datetime_to)\n", "data = data.pivot(index='datetime', columns='symbol', values='close')\n", "\n", "# #strategy\n", "# strategy = MyStrategy(symbol_ids)\n", "\n", "# #backtest engine\n", "# engine = BacktestEngine(starting_balance, base_currency, track_performance)\n", "# engine.add_data_source(data)\n", "# engine.add_strategy(strategy)\n", "    \n", "# #run backtest\n", "# engine.start()\n", "\n", "# #get statistical performance data\n", "# stats = None\n", "# if calculate_performance:\n", "#     stats = engine.get_performance_statistics(visualize_performance)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (3_383, 29)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>datetime</th><th>AUDCAD</th><th>AUDCHF</th><th>AUDJPY</th><th>AUDNZD</th><th>AUDUSD</th><th>CADCHF</th><th>CADJPY</th><th>CHFJPY</th><th>EURAUD</th><th>EURCAD</th><th>EURCHF</th><th>EURGBP</th><th>EURJPY</th><th>EURNZD</th><th>EURUSD</th><th>GBPAUD</th><th>GBPCAD</th><th>GBPCHF</th><th>GBPJPY</th><th>GBPNZD</th><th>GBPUSD</th><th>NZDCAD</th><th>NZDCHF</th><th>NZDJPY</th><th>NZDUSD</th><th>USDCAD</th><th>USDCHF</th><th>USDJPY</th></tr><tr><td>datetime[ns, UTC]</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td><td>f64</td></tr></thead><tbody><tr><td>2010-01-03 22:00:00 UTC</td><td>0.9504</td><td>0.93968</td><td>84.425003</td><td>1.24275</td><td>0.91273</td><td>0.98837</td><td>88.809998</td><td>89.82</td><td>1.57935</td><td>1.501275</td><td>1.4843</td><td>0.896125</td><td>133.330002</td><td>1.96222</td><td>1.44128</td><td>1.76214</td><td>1.67633</td><td>1.65635</td><td>148.764999</td><td>2.18993</td><td>1.60845</td><td>0.764655</td><td>0.75603</td><td>67.898499</td><td>0.7343</td><td>1.04165</td><td>1.029825</td><td>92.5075</td></tr><tr><td>2010-01-04 22:00:00 UTC</td><td>0.94708</td><td>0.94234</td><td>83.625</td><td>1.2414</td><td>0.91235</td><td>0.99452</td><td>88.260002</td><td>88.737503</td><td>1.5754</td><td>1.4925</td><td>1.484775</td><td>0.8983</td><td>131.740005</td><td>1.95515</td><td>1.43647</td><td>1.75332</td><td>1.66103</td><td>1.6527</td><td>146.649994</td><td>2.17632</td><td>1.599125</td><td>0.762905</td><td>0.75905</td><td>67.342499</td><td>0.734475</td><td>1.038975</td><td>1.03345</td><td>91.702499</td></tr><tr><td>2010-01-05 22:00:00 UTC</td><td>0.94921</td><td>0.94492</td><td>84.917503</td><td>1.246375</td><td>0.92014</td><td>0.99481</td><td>89.422501</td><td>89.839996</td><td>1.566375</td><td>1.48763</td><td>1.48065</td><td>0.899425</td><td>133.009995</td><td>1.95249</td><td>1.4407</td><td>1.74107</td><td>1.65326</td><td>1.6461</td><td>147.882507</td><td>2.17057</td><td>1.60185</td><td>0.761355</td><td>0.75789</td><td>68.086502</td><td>0.737725</td><td>1.0324</td><td>1.027575</td><td>92.32</td></tr><tr><td>2010-01-06 22:00:00 UTC</td><td>0.94886</td><td>0.94844</td><td>85.654999</td><td>1.25315</td><td>0.91769</td><td>0.99903</td><td>90.239998</td><td>90.294998</td><td>1.5596</td><td>1.48035</td><td>1.479525</td><td>0.898125</td><td>133.595001</td><td>1.95385</td><td>1.43053</td><td>1.7363</td><td>1.64788</td><td>1.647475</td><td>148.767502</td><td>2.1755</td><td>1.5931</td><td>0.756735</td><td>0.75643</td><td>68.287498</td><td>0.731925</td><td>1.03465</td><td>1.0341</td><td>93.370003</td></tr><tr><td>2010-01-07 22:00:00 UTC</td><td>0.95203</td><td>0.94658</td><td>85.677498</td><td>1.255075</td><td>0.9238</td><td>0.99365</td><td>89.962502</td><td>90.485001</td><td>1.558275</td><td>1.484085</td><td>1.4753</td><td>0.899275</td><td>133.509995</td><td>1.95486</td><td>1.44091</td><td>1.73214</td><td>1.64958</td><td>1.640775</td><td>148.460007</td><td>2.17328</td><td>1.6023</td><td>0.758145</td><td>0.75385</td><td>68.226501</td><td>0.73675</td><td>1.029875</td><td>1.023975</td><td>92.660004</td></tr><tr><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td><td>&hellip;</td></tr><tr><td>2022-12-25 22:00:00 UTC</td><td>0.91371</td><td>0.62711</td><td>89.377502</td><td>1.06892</td><td>0.67271</td><td>0.686335</td><td>97.824997</td><td>142.594498</td><td>1.58123</td><td>1.44461</td><td>0.991495</td><td>0.88136</td><td>141.326508</td><td>1.68924</td><td>1.06372</td><td>1.79371</td><td>1.63855</td><td>1.12416</td><td>160.348007</td><td>1.916415</td><td>1.206285</td><td>0.85525</td><td>0.58667</td><td>83.624496</td><td>0.62967</td><td>1.35838</td><td>0.932185</td><td>132.859497</td></tr><tr><td>2022-12-26 22:00:00 UTC</td><td>0.91038</td><td>0.62568</td><td>89.865501</td><td>1.07263</td><td>0.673135</td><td>0.687</td><td>98.711502</td><td>143.693497</td><td>1.580675</td><td>1.43883</td><td>0.98846</td><td>0.884285</td><td>142.050507</td><td>1.69555</td><td>1.06399</td><td>1.787185</td><td>1.62706</td><td>1.118015</td><td>160.602997</td><td>1.91734</td><td>1.202935</td><td>0.84876</td><td>0.583175</td><td>83.766502</td><td>0.627615</td><td>1.35234</td><td>0.92901</td><td>133.496002</td></tr><tr><td>2022-12-27 22:00:00 UTC</td><td>0.916785</td><td>0.626155</td><td>90.6035</td><td>1.067715</td><td>0.673775</td><td>0.68293</td><td>98.856499</td><td>144.747498</td><td>1.57505</td><td>1.443765</td><td>0.9858</td><td>0.88297</td><td>142.699493</td><td>1.682585</td><td>1.061235</td><td>1.78353</td><td>1.634795</td><td>1.116665</td><td>161.580002</td><td>1.90431</td><td>1.20176</td><td>0.85862</td><td>0.58596</td><td>84.789001</td><td>0.63108</td><td>1.360445</td><td>0.929215</td><td>134.471497</td></tr><tr><td>2022-12-28 22:00:00 UTC</td><td>0.9183</td><td>0.625535</td><td>90.183998</td><td>1.0677</td><td>0.67792</td><td>0.681385</td><td>98.183998</td><td>144.099503</td><td>1.572675</td><td>1.44448</td><td>0.98436</td><td>0.884455</td><td>141.837494</td><td>1.67993</td><td>1.066155</td><td>1.778345</td><td>1.633355</td><td>1.112905</td><td>160.384995</td><td>1.89951</td><td>1.20554</td><td>0.86003</td><td>0.585815</td><td>84.439499</td><td>0.634685</td><td>1.35489</td><td>0.92302</td><td>133.028503</td></tr><tr><td>2022-12-29 22:00:00 UTC</td><td>0.9236</td><td>0.63062</td><td>89.336502</td><td>1.07321</td><td>0.681455</td><td>0.6828</td><td>96.807999</td><td>141.761505</td><td>1.57104</td><td>1.450495</td><td>0.990055</td><td>0.884875</td><td>140.363007</td><td>1.685935</td><td>1.07068</td><td>1.775135</td><td>1.63891</td><td>1.11903</td><td>158.613495</td><td>1.90446</td><td>1.209515</td><td>0.86044</td><td>0.58744</td><td>83.254997</td><td>0.63485</td><td>1.354835</td><td>0.92456</td><td>131.109497</td></tr></tbody></table></div>"], "text/plain": ["shape: (3_383, 29)\n", "┌──────────────┬──────────┬──────────┬───────────┬───┬──────────┬──────────┬──────────┬────────────┐\n", "│ datetime     ┆ AUDCAD   ┆ AUDCHF   ┆ AUDJPY    ┆ … ┆ NZDUSD   ┆ USDCAD   ┆ USDCHF   ┆ USDJPY     │\n", "│ ---          ┆ ---      ┆ ---      ┆ ---       ┆   ┆ ---      ┆ ---      ┆ ---      ┆ ---        │\n", "│ datetime[ns, ┆ f64      ┆ f64      ┆ f64       ┆   ┆ f64      ┆ f64      ┆ f64      ┆ f64        │\n", "│ UTC]         ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "╞══════════════╪══════════╪══════════╪═══════════╪═══╪══════════╪══════════╪══════════╪════════════╡\n", "│ 2010-01-03   ┆ 0.9504   ┆ 0.93968  ┆ 84.425003 ┆ … ┆ 0.7343   ┆ 1.04165  ┆ 1.029825 ┆ 92.5075    │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "│ 2010-01-04   ┆ 0.94708  ┆ 0.94234  ┆ 83.625    ┆ … ┆ 0.734475 ┆ 1.038975 ┆ 1.03345  ┆ 91.702499  │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "│ 2010-01-05   ┆ 0.94921  ┆ 0.94492  ┆ 84.917503 ┆ … ┆ 0.737725 ┆ 1.0324   ┆ 1.027575 ┆ 92.32      │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "│ 2010-01-06   ┆ 0.94886  ┆ 0.94844  ┆ 85.654999 ┆ … ┆ 0.731925 ┆ 1.03465  ┆ 1.0341   ┆ 93.370003  │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "│ 2010-01-07   ┆ 0.95203  ┆ 0.94658  ┆ 85.677498 ┆ … ┆ 0.73675  ┆ 1.029875 ┆ 1.023975 ┆ 92.660004  │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "│ …            ┆ …        ┆ …        ┆ …         ┆ … ┆ …        ┆ …        ┆ …        ┆ …          │\n", "│ 2022-12-25   ┆ 0.91371  ┆ 0.62711  ┆ 89.377502 ┆ … ┆ 0.62967  ┆ 1.35838  ┆ 0.932185 ┆ 132.859497 │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "│ 2022-12-26   ┆ 0.91038  ┆ 0.62568  ┆ 89.865501 ┆ … ┆ 0.627615 ┆ 1.35234  ┆ 0.92901  ┆ 133.496002 │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "│ 2022-12-27   ┆ 0.916785 ┆ 0.626155 ┆ 90.6035   ┆ … ┆ 0.63108  ┆ 1.360445 ┆ 0.929215 ┆ 134.471497 │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "│ 2022-12-28   ┆ 0.9183   ┆ 0.625535 ┆ 90.183998 ┆ … ┆ 0.634685 ┆ 1.35489  ┆ 0.92302  ┆ 133.028503 │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "│ 2022-12-29   ┆ 0.9236   ┆ 0.63062  ┆ 89.336502 ┆ … ┆ 0.63485  ┆ 1.354835 ┆ 0.92456  ┆ 131.109497 │\n", "│ 22:00:00 UTC ┆          ┆          ┆           ┆   ┆          ┆          ┆          ┆            │\n", "└──────────────┴──────────┴──────────┴───────────┴───┴──────────┴──────────┴──────────┴────────────┘"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}