{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "import polars as pl\n", "from mattlibrary.backtesting.enumerables import DataType\n", "from mattlibrary.backtesting.base_strategy import StrategyBase\n", "from mattlibrary.backtesting.backtest_engine import BacktestEngine\n", "from mattlibrary.datamanagement.clickhouse import ClickHouseClient\n", "\n", "%reload_ext autoreload\n", "%autoreload 2\n", "\n", "clickhouse_client = ClickHouseClient()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class MyStrategy(StrategyBase):\n", "\n", "    def __init__(self, symbol:str):\n", "        self.symbol = symbol\n", "        self.strategy_id = \"MyStrategy\"\n", "        self.description = \"My first strategy\"\n", "        self.capture_performance_datapoints = True\n", "        super().__init__(self.strategy_id, self.description)\n", "               \n", "    def initialize(self):\n", "        self.notional_size = 100_000\n", "        self.previous_price = 0.0\n", "        self.previous_sma = 0.0\n", "        self.iterator = 0\n", "        \n", "    def process_data(self, index, df):\n", "        \n", "        timestamp = df[\"datetime\"][index]\n", "        price = df[self.symbol][index]\n", "        sma = df[\"sma\"][index]\n", "                \n", "        #generate signal and orders\n", "        if price >= sma and self.previous_price < self.previous_sma:\n", "            #submit buy order\n", "            order_size = self.notional_size / price\n", "            super().submit_order(self.symbol, timestamp, order_size, price, True)\n", "        elif price <= sma and self.previous_price > self.previous_sma:\n", "            #submit sell order\n", "            order_size = self.notional_size / price\n", "            super().submit_order(self.symbol, timestamp, -order_size, price, True)    \n", "                \n", "        #update previous price and sma\n", "        self.previous_price = price\n", "        self.previous_sma = sma\n", "                    \n", "    def finalize(self):\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_currency = \"USD\"\n", "starting_balance = 100_000\n", "symbol = \"TSLA\"\n", "table_name = \"polygon_stocks_daily\"\n", "data_type = DataType.OhlcData\n", "datetime_from = datetime.datetime(2010, 1, 1)\n", "datetime_to = datetime.datetime(2022, 12, 31)\n", "sma_window_size = 15\n", "track_performance = True\n", "calculate_performance = True\n", "visualize_performance = True\n", "\n", "#data\n", "data = clickhouse_client.request_marketdata(table_name, [symbol], datetime_from, datetime_to)\n", "data = data.sort(\"symbol\", \"datetime\").with_columns(sma=pl.col(\"close\").rolling_mean(window_size=sma_window_size).over(\"symbol\")).drop_nulls().sort(\"datetime\")\n", "data = data.select(pl.col(\"datetime\"), pl.col(\"close\").alias(symbol), pl.col(\"sma\"))\n", "\n", "#strategy\n", "strategy = MyStrategy(symbol)\n", "\n", "#backtest engine\n", "engine = BacktestEngine(starting_balance, base_currency, track_performance)\n", "engine.add_data_source(data)\n", "engine.add_strategy(strategy)\n", "    \n", "#run backtest\n", "engine.start()\n", "\n", "#get statistical performance data\n", "stats = None\n", "if calculate_performance:\n", "    stats = engine.get_performance_statistics(visualize_performance)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}