"""Enumeration of child order types for trading system."""
from enum import Enum

class ChildOrderType(Enum):
    """Enumeration of child order types.
    
    Attributes:
        MARKET: Market order, executed immediately at current market price
        LIMIT: Limit order, executed only at specified price or better
        STOP: Stop order, triggers a market order when price reaches specified level
    """
    MARKET = 1
    LIMIT = 2
    STOP = 3,
    STOPLIMIT = 4

    def __str__(self):
        """
        Returns the string representation of the Enum member (its name).
        This is what <PERSON><PERSON> will use for automatic conversion.
        """
        return self.name
    
    def __repr__(self):
        """
        Returns the official string representation, useful for debugging.
        """
        return self.name