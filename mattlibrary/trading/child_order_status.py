"""Enumeration of child order statuses for trading system."""
from enum import Enum

class ChildOrderStatus(Enum):
    """Enumeration of child order statuses.
    
    Attributes:
        NEW: Order has been created but not yet processed
        SUBMITTED: Order has been submitted to the execution engine
        PARTIALLY_FILLED: Order has been partially filled
        FILLED: Order has been completely filled
        CANCELED: Order has been canceled
        REJECTED: Order has been rejected
    """
    NEW = 1
    SUBMITTED = 2
    CANCELATION_REQUESTED = 3
    FILLED = 4
    CANCELED = 5

    def __str__(self):
        """
        Returns the string representation of the Enum member (its name).
        This is what <PERSON><PERSON> will use for automatic conversion.
        """
        return self.name
    
    def __repr__(self):
        """
        Returns the official string representation, useful for debugging.
        """
        return self.name