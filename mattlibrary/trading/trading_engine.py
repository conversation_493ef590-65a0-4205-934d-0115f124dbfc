"""Trading Engine module for coordinating order and position management."""

import logging
import copy
from typing import List, Dict, Callable
from mattlibrary.trading.child_order import <PERSON><PERSON><PERSON><PERSON>
from mattlibrary.trading.child_order_status import ChildOrderStatus
from mattlibrary.trading.fx_converter import Fx_Converter
from mattlibrary.trading.symbol_database import SymbolDatabase
from mattlibrary.trading.trade import Trade
from mattlibrary.trading.position import Position
from mattlibrary.trading.parent_order_base import ParentOrderBase
from mattlibrary.trading.parent_order_status import ParentOrderStatus
from mattlibrary.trading.execution_plugin_base import ExecutionPluginBase
from mattlibrary.trading.strategy_base import StrategyBase


class TradingEngine:
    """
    Central trading engine for executing trading strategies.
    
    The TradingEngine coordinates between market data feeds, trading strategies,
    order execution, and position management. It supports:
    
    - Multiple concurrent trading strategies
    - Real-time market data processing
    - Multi-asset portfolio management
    - Performance tracking and analytics
    
    Args:
        symbol_dict: Mapping of symbol IDs to symbol information
        fx_converter: Foreign exchange rate converter
        execution_plugin_type: Type of execution plugin to use
        track_performance: Whether to track detailed performance metrics
        
    Example:
        >>> engine = TradingEngine(
        ...     symbol_dict=symbols,
        ...     fx_converter=fx_conv,
        ...     execution_plugin_type="SimulatedExecutionEngine",
        ...     track_performance=True
        ... )
        >>> engine.add_strategy(my_strategy)
        >>> engine.process_market_data(market_data)
    """

    def __init__(self, starting_balance: int, base_currency: str, track_performance: bool, execution_plugin_type: str):
        """Initialize the Trading Engine.
        
        Args:
            starting_balance: Initial capital amount for performance tracking
            base_currency: Base currency for P&L calculations (e.g., 'USD', 'EUR')
            track_performance: Whether to track detailed performance metrics
        """
        self.logger = logging.getLogger(__name__)
        
        # Store initialization parameters
        self.starting_balance = starting_balance
        self.base_currency = base_currency
        self.track_performance = track_performance
        self.execution_engine: ExecutionPluginBase = None
        
        # Initialize execution engine with callback to handle fills
        if execution_plugin_type == "SimulatedExecutionEngine":
            from mattlibrary.trading.execution_engines.simulated_execution_engine import SimulatedExecutionEngine
            self.execution_engine = SimulatedExecutionEngine(self.child_order_from_execution_engine)
        else:
            raise ValueError(f"Invalid execution plugin type: {execution_plugin_type}")

        # Initialize strategies dictionary
        self.strategies = dict() # strategy_id -> StrategyBase
        
        # Initialize parent orders dictionary
        self.working_parent_orders: Dict[str, ParentOrderBase] = dict() # order_id -> ParentOrderBase
        
        # Initialize position tracking (from PMS)
        self.fx_converter = Fx_Converter(base_currency)
        self.symbol_dict = SymbolDatabase().get_symbol_dictionary()
        self.current_positions: Dict[str, Dict[str, Position]] = dict() # symbol_id -> {strategy_id -> Position}
        
        # Variables and Collections for tracking (from PMS)
        self.completed_parent_orders: List[ParentOrderBase] = []
        self.trades: List[Trade] = []
        self.position_records: List[Position] = []
        
        self.logger.info(f"Initialized Trading Engine [starting_balance={starting_balance} {base_currency}, track_performance={track_performance}]")


    def add_strategy(self, strategy: StrategyBase):
        """Add a strategy to the trading engine.
        
        Args:
            strategy: The strategy to add, must inherit from StrategyBase
        """
        # Check for duplicate strategy IDs
        if strategy.strategy_id in self.strategies:
            self.logger.error(f"Duplicate strategy ID: {strategy.strategy_id}")
            raise ValueError(f"Strategy with strategy_id {strategy.strategy_id} already exists.")

        # Create positions from strategy and add to current_positions
        strategy_positions = {}
        for symbol_id in strategy.symbols:

            position = Position(strategy.strategy_id, symbol_id)
            strategy_positions[symbol_id] = position

            if symbol_id not in self.current_positions:
                self.current_positions[symbol_id] = {}
            self.current_positions[symbol_id][strategy.strategy_id] = position

        # Configure the strategy with callbacks and positions
        strategy.configure_strategy(self.submit_parent_order, strategy_positions)
        self.strategies[strategy.strategy_id] = strategy
        self.logger.info(f"Added strategy [id={strategy.strategy_id}, description={strategy.description}]")


    def remove_strategy(self, strategy: StrategyBase):
        """Remove a strategy from the trading engine.
        
        Args:
            strategy: The strategy to remove
        """
        if strategy.strategy_id not in self.strategies:
            self.logger.error(f"Strategy not found: {strategy.strategy_id}")
            raise ValueError(f"Strategy with strategy_id {strategy.strategy_id} does not exist.")

        # Remove order submission callback from strategy
        strategy.remove_order_submission_callback()
        
        # Remove the strategy
        del self.strategies[strategy.strategy_id]
        
        self.logger.info(f"Removed strategy [id={strategy.strategy_id}]")
    

    def submit_parent_order(self, parent_order: ParentOrderBase):
        """Submit a parent order to the Trading Engine and register callback for child order handling.
        
        Args:
            parent_order: The parent order to submit
        """
        # Store the parent order
        self.working_parent_orders[parent_order.order_id] = parent_order

        # Register callback for child order handling
        parent_order.register_child_order_submission_callback(self.child_order_from_parent_order)

        self.logger.info(f"Parent order submitted: {parent_order.order_id} for {parent_order.symbol_ids}")

        
    def child_order_from_parent_order(self, child_order: ChildOrder):
        """Handle child order from a parent order.
        
        Args:
            child_order: The child order to handle
        """
        #submit child order to execution engine
        self.execution_engine.submit_child_order(child_order)


    def child_order_from_execution_engine(self, child_order: ChildOrder):
        """Handle child order from the execution engine.
        
        Args:
            child_order: filled, canceled, or rejected child order
        """

        # Update position with the fill (only if the child order is filled)
        if child_order.status == ChildOrderStatus.FILLED:
            self.update_position_with_childorder_fill(child_order)

        # Find the parent order that generated this child order
        parent_order_id = child_order.parent_order_id
        if parent_order_id not in self.working_parent_orders:
            self.logger.error(f"Parent order not found for child order: {child_order.order_id}")
            raise ValueError(f"Parent order not found for child order: {child_order.order_id}")
        
        # Process the fill in the parent order
        parent_order = self.working_parent_orders[parent_order_id]
        parent_order.process_fill(child_order)

        # remove parent order if completed
        if parent_order.order_status == ParentOrderStatus.COMPLETED:
            #check if parent order is still in working parent orders
            if parent_order_id in self.working_parent_orders:
                
                #notify strategy of its parent order completion
                self.strategies[parent_order.strategy_id].parent_order_completed_notification(parent_order)
                
                #remove parent order from working parent orders
                del self.working_parent_orders[parent_order_id]
                # Record the parent order in performance metrics if tracking is enabled
                if self.track_performance:
                    self.completed_parent_orders.append(parent_order)
                # Log completion
                self.logger.info(f"Parent order completed: {parent_order.order_id}")
            

    def process_market_data(self, row):
        """Process current market data, update positions, and run strategies.
        
        Args:
            row: A data row containing market data
        """

        # Extract data from the row as function of the type of data
        symbol_id = row["symbol"]
        time_stamp = row["datetime"]
        bid_price = row["close"]
        ask_price = row["close"]
        
        # 1. Feed market data to execution engine for order processing
        self.execution_engine.process_market_data(symbol_id, time_stamp, bid_price, ask_price)

        # 2. Process data through each strategy
        for strategy in self.strategies.values():
            strategy.process_data(row)
        
        # 3. Pass market data to each parent order (if symbol matches)
        for parent_order_id in list(self.working_parent_orders.keys()):
            parent_order = self.working_parent_orders[parent_order_id]
            if symbol_id in parent_order.symbol_ids:
                parent_order.process_market_data(symbol_id, time_stamp, bid_price, ask_price)

        # 4. Update positions with market data
        self.update_positions_with_market_data(symbol_id, time_stamp, bid_price, ask_price)

        # 5. Record positions (copy) for performance tracking
        if self.track_performance:
            for position in self.current_positions[symbol_id].values():
                self.position_records.append(copy.copy(position))


    def finalize(self):
        """Finalize the Trading Engine."""
        # Finalize each strategy
        for strategy in self.strategies.values():
            strategy.finalize()
        
        self.logger.info(f"Trading Engine finalized [strategies={len(self.strategies)}]")


    def get_performance_data(self) -> tuple[list[ParentOrderBase], list[Trade], list[Position]]:
        """Returns a tuple containing all the data needed to generate performance metrics."""
        return (self.completed_parent_orders, self.trades, self.position_records)


    def update_position_with_childorder_fill(self, child_order: ChildOrder):
        """Update positions with the current child order fill.
        
        Args:
            child_order: The filled order
        """
        
        # Get or create position
        position = self.current_positions[child_order.symbol_id][child_order.strategy_id]
        
        # Update position timestamp and current price
        position.timestamp_current = child_order.timestamp_filled
        position.current_price = child_order.filled_price

        # Store current position details for P&L calculations
        current_size = position.position_size
        
        # Calculate new position size 
        new_size = current_size + child_order.filled_size
        


        prev_avg_price = position.average_price
        
        # Get FX conversion rate for P&L calculations in base currency
        fx_rate = self.fx_converter.get_fx_conversion_rate(
            child_order.timestamp_filled, 
            self.symbol_dict[child_order.symbol_id].baseCurrency
        )

        # Case 1: Position increasing in same direction (adding to long or adding to short)
        if (current_size >= 0 and child_order.filled_size > 0) or (current_size <= 0 and child_order.filled_size < 0):
            if current_size == 0:
                # New position - set average price to current price
                position.average_price = child_order.filled_price
                position.timestamp_first_fill = child_order.timestamp_filled
            else:
                # Adding to existing position - calculate new average price weighted by position sizes
                position.average_price = (abs(child_order.filled_size * child_order.filled_price) + abs(current_size * prev_avg_price)) / (abs(child_order.filled_size) + abs(current_size))
            position.position_size = new_size
            return

        # Case 2: Partial position reduction (reducing long or reducing short, but not crossing zero)
        if abs(current_size) > abs(child_order.filled_size):
            # Calculate realized P&L for the portion being closed
            realized_pnl_local = (child_order.filled_price - prev_avg_price) * abs(child_order.filled_size) * (1 if current_size > 0 else -1)
            realized_pnl_base = realized_pnl_local * fx_rate
            
            # Update position's realized P&L
            position.realized_pnl_local += realized_pnl_local
            position.realized_pnl_base += realized_pnl_base
            position.position_size = new_size

            # Record trade in performance metrics if tracking is enabled
            if self.track_performance:
                # trade size (should be signed of original direction)
                trade_size = abs(child_order.filled_size) * (1 if current_size > 0 else -1)
                self.trades.append(Trade(position.strategy_id, position.symbol_id,
                               position.timestamp_first_fill, child_order.timestamp_filled, position.average_price, child_order.filled_price, 
                               trade_size, realized_pnl_local, realized_pnl_base))
            return

        # Case 3: Position crosses zero (flipping from long to short or short to long)
        # Calculate realized P&L for closing the entire existing position
        realized_pnl_local = (child_order.filled_price - prev_avg_price) * current_size
        realized_pnl_base = realized_pnl_local * fx_rate
        position.realized_pnl_local += realized_pnl_local
        position.realized_pnl_base += realized_pnl_base

        # Record trade in performance metrics if tracking is enabled
        if self.track_performance:
            # trade size (should be signed of original direction)
            trade_size = abs(current_size) * (1 if current_size > 0 else -1)
            self.trades.append(Trade(position.strategy_id, position.symbol_id,
                               position.timestamp_first_fill, child_order.timestamp_filled, position.average_price, child_order.filled_price, 
                               trade_size, realized_pnl_local, realized_pnl_base))

        # Update position details based on whether it's closed completely or reversed
        if new_size == 0:
            # Position closed completely
            position.average_price = 0.0
            position.timestamp_first_fill = None
        else:
            # Position reversed (e.g., from long to short) - treat remaining size as a new position
            position.average_price = child_order.filled_price
            position.timestamp_first_fill = child_order.timestamp_filled
        position.position_size = new_size


    def update_positions_with_market_data(self, symbol_id, time_stamp, bid_price, ask_price):
        """Update positions with the current market data.
        
        Args:
            symbol_id: The symbol identifier
            time_stamp: The timestamp of the market data
            bid_price: The current bid price
            ask_price: The current ask price
        """

        for position in self.current_positions[symbol_id].values():
            
            # Update position with current timestamp and price
            position.timestamp_current = time_stamp
            position.current_price = (bid_price + ask_price) / 2

            # Calculate unrealized P&L
            if position.position_size == 0:
                # No position, no unrealized P&L
                position.unrealized_pnl_local = 0.0
                position.unrealized_pnl_base = 0.0
            else:
                # Calculate unrealized P&L in local currency
                position.unrealized_pnl_local = (position.current_price - position.average_price) * position.position_size
                # Convert to base currency
                position.unrealized_pnl_base = position.unrealized_pnl_local * \
                    self.fx_converter.get_fx_conversion_rate(time_stamp, self.symbol_dict[symbol_id].baseCurrency)