"""Backtest engine module for running trading strategies on historical data."""


import logging
import datetime as dt
import os
import shutil
import importlib
from typing import List
import polars as pl
import inspect
from mattlibrary.trading.strategy_base import StrategyBase
from mattlibrary.trading.trading_engine import TradingEngine
from mattlibrary.backtesting.backtest_configuration import BacktestConfiguration
from mattlibrary.logging import logging_config
from mattlibrary.logging.excel_logging import excel_logger
from mattlibrary.trading.performance_metrics import PerformanceMetrics


class BacktestEngine:
    """Engine for running backtests on trading strategies with historical data."""
    

    def __init__(self, configuration: BacktestConfiguration):
        
        # Store configuration
        self.configuration = configuration

        # Initialize the trading engine
        self.trading_engine = TradingEngine(self.configuration.starting_balance, self.configuration.base_currency, self.configuration.track_performance, self.configuration.execution_plugin_type)
        
        # generate strategies and attach to trading engine
        self.strategies = self._strategy_generator()
        for strategy in self.strategies:
            self.trading_engine.add_strategy(strategy)

        # generate data source
        self.data_source = self._data_generator()

        #validate data source
        if not isinstance(self.data_source, pl.DataFrame):
            self.logger.error(f"Invalid data source type: {type(self.data_source)}. Expected Polars DataFrame")
            raise TypeError("Data source must be a Polars DataFrame")
            
        if len(self.data_source) == 0:
            self.logger.error("Empty data source provided")
            raise ValueError("Data source contains no rows")

        #validate strategies
        if len(self.strategies) == 0:
            self.logger.error("No strategies provided")
            raise ValueError("At least one strategy must be provided")
        for strategy in self.strategies:
            if not isinstance(strategy, StrategyBase):
                self.logger.error(f"Invalid strategy type: {type(strategy)}. Expected StrategyBase object")
                raise TypeError("Strategies must be a list of StrategyBase objects")

        # Configure directories
        self._configure_directories()

        # Configure logging
        self._configure_logging()

        #store meta data
        self.store_meta_data()

        #log message
        self.logger.info(f"Initialized BacktestEngine [logging_engabled={self.configuration.logging_enabled}, strategy_excel_logging={self.configuration.strategy_excel_logging}, statistics_excel_logging={self.configuration.statistics_excel_logging}, track_performance={self.configuration.track_performance}]")


    def _strategy_generator(self) -> List[StrategyBase]:
        from mattlibrary.trading.trading_strategies.sma_cross_over.sma_cross_over import SmaCrossoverStrategy

        strategies = []
        for symbol in self.configuration.symbols_traded:
            strategy = SmaCrossoverStrategy(
                symbol, 
                self.configuration.sma_window_size, 
                self.configuration.order_size, 
                self.configuration.is_target_size, 
                self.configuration.strategy_excel_logging
                )
            strategies.append(strategy)
        return strategies
        

    def _data_generator(self) -> pl.DataFrame:
        import polars as pl
        from mattlibrary.datamanagement.polars_db import PolarsDataFrameManager

        manager = PolarsDataFrameManager(base_directory=str(self.configuration.data_directory))
        filter_expr = pl.col("symbol").is_in(self.configuration.symbols_data) & (pl.col("datetime").is_between(self.configuration.start_dt, self.configuration.end_dt))
        df = manager.read_data(dataset_name=self.configuration.dataset_name, filter_expr=filter_expr)
        source_data = df.sort("symbol", "datetime").with_columns(sma=pl.col("close").rolling_mean(window_size=self.configuration.sma_window_size).over("symbol")).drop_nulls().sort("datetime")
        return source_data


    def _configure_directories(self):
        # Store configuration and backtest name and directory
        self.backtest_name = f"backtest_{dt.datetime.now().strftime('%Y-%b-%d %H-%M-%S')}"
        self.base_directory = self.configuration.backtests_base_directory
        self.backtest_directory = os.path.join(self.base_directory, self.backtest_name)
        self.engine_log_directory = os.path.join(self.backtest_directory, "logs")
        self.excel_directory = os.path.join(self.backtest_directory, "excel")
        self.strategy_directory = os.path.join(self.backtest_directory, "strategies")
        self.performance_directory = os.path.join(self.backtest_directory, "performance")

        #create backtests directory if it does not yet exist
        if not os.path.exists(self.base_directory):
            os.makedirs(self.base_directory)

        #if requested, delete all subdirectories under base_directory (including all directories that contain files)
        if self.configuration.clean_directory_on_init:
            for root, dirs, files in os.walk(self.base_directory, topdown=False):
                for name in dirs:
                    dir_path = os.path.join(root, name)
                    shutil.rmtree(dir_path)

        #create directory and subdirectories for this backtest
        os.makedirs(self.backtest_directory)
        os.makedirs(self.engine_log_directory)
        os.makedirs(self.excel_directory)
        os.makedirs(self.strategy_directory)
        os.makedirs(self.performance_directory)


    def _configure_logging(self):
        #register local logger
        self.logger = logging.getLogger(__name__)

        #configure logging 
        logging_config.setup_logging(
            logging_enabled=self.configuration.logging_enabled, 
            log_level=logging.DEBUG, 
            log_to_file=self.configuration.log_to_file,
            log_file=os.path.join(self.engine_log_directory, "engine.log"), 
            log_to_console=self.configuration.log_to_console, 
            clean_log_file=True)

        #configure strategy excel logger
        excel_logger.configure_base_directory(self.excel_directory)


    def store_meta_data(self):
        #store configuration
        with open(os.path.join(self.backtest_directory, "configuration.json"), "w") as f:
            f.write(self.configuration.to_json())

        #store strategy generation code
        strategy_generator_code = inspect.getsource(self._strategy_generator)
        with open(os.path.join(self.backtest_directory, "strategy_generation_code.py"), "w") as f:
            f.write(strategy_generator_code)
       
        #store data generation code
        data_module = inspect.getsource(self._data_generator)
        with open(os.path.join(self.backtest_directory, "data_generation_code.py"), "w") as f:
            f.write(data_module)
        
        #store the actual code of the strategies, used
        unique_strategy_types = set([type(x) for x in self.strategies])
        for strategy_type in unique_strategy_types:
            strategy_path_filename = inspect.getfile(strategy_type)
            strategy_name = os.path.basename(strategy_path_filename)
            shutil.copy(strategy_path_filename, os.path.join(self.strategy_directory, strategy_name))
    
        
    def start_backtest(self):
        """Start the backtest."""
        row_count = len(self.data_source)
        strategy_count = len(self.trading_engine.strategies)

        self.logger.info(f"Starting data iteration [rows={row_count}, active_strategies={strategy_count}]")
        
        try:
            for row in self.data_source.iter_rows(named=True):
                # Process each row through the trading engine
                self.trading_engine.process_market_data(row)
            self.logger.info(f"Data iteration complete [processed_rows={row_count}, total_strategies={strategy_count}]")
        except Exception as e:
            self.logger.error(f"Error during data iteration: {str(e)}")
            raise

        #finalize backtest
        self.logger.info("Finalizing backtest")
        self.trading_engine.finalize()

        #generate performance statistics
        if self.configuration.track_performance:
            parent_orders, trades, positions = self.trading_engine.get_performance_data()
            performance_statistics = PerformanceMetrics(self.configuration)
            performance_statistics.generate_statistics(row_count, parent_orders, trades, positions)

            #write performance metrics to disk
            with pl.Config(tbl_rows=100, tbl_cols=-1):
                # Set to a sufficiently large number to show all rows
                # Set to -1 to show all columns (no truncation)
                metrics_string = performance_statistics.metrics.__repr__()
            with open(os.path.join(self.performance_directory, "performance_metrics.txt"), "w") as f:
                f.write(metrics_string)


            self.child_orders = performance_statistics.child_orders

            performance_statistics.metrics.write_parquet(os.path.join(self.performance_directory, "performance_metrics.parquet"))
            performance_statistics.data_series.write_parquet(os.path.join(self.performance_directory, "data_series.parquet"))
            performance_statistics.weekly_pnl_series.write_parquet(os.path.join(self.performance_directory, "weekly_pnl_series.parquet"))
            performance_statistics.monthly_pnl_series.write_parquet(os.path.join(self.performance_directory, "monthly_pnl_series.parquet"))
            performance_statistics.annual_pnl_series.write_parquet(os.path.join(self.performance_directory, "annual_pnl_series.parquet"))
            performance_statistics.trades.write_parquet(os.path.join(self.performance_directory, "trades.parquet"))
            performance_statistics.positions.write_parquet(os.path.join(self.performance_directory, "positions.parquet"))
            performance_statistics.child_orders.write_parquet(os.path.join(self.performance_directory, "child_orders.parquet"))
            
            if self.configuration.statistics_excel_logging:
                performance_statistics.export_statistics_to_excel(self.excel_directory)

        #log message
        self.logger.info(f"Backtest successfully finalized")
    

    def load_backtest_modules(self, backtest_path: str) -> tuple[BacktestConfiguration, List[StrategyBase], pl.DataFrame]:
        """Load a backtest configuration from a file.
        
        Args:
            file_name: Name of the file to load
        """
        #load configuration
        configuration_path_filename = os.path.join(backtest_path, "configuration.json")
        with open(configuration_path_filename, "r") as f:
            configuration = BacktestConfiguration.from_json(f.read())
        
        #load strategy generation code
        strategy_path_filename = os.path.join(backtest_path, "strategy_generation_code.py")
        spec = importlib.util.spec_from_file_location("strategy_generation_code", strategy_path_filename)
        strategy_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(strategy_module)
        generate_strategies = strategy_module.generate_strategies
        strategies = generate_strategies(configuration)

        #load data generation code
        data_path_filename = os.path.join(backtest_path, "data_generation_code.py")
        spec = importlib.util.spec_from_file_location("data_generation_code", data_path_filename)
        data_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(data_module)
        generate_data = data_module.generate_data
        source_data = generate_data(configuration)

        return configuration, strategies, source_data